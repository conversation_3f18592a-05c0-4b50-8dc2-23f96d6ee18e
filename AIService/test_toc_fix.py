#!/usr/bin/env python3
"""
Test script to verify that table of contents generation no longer abbreviates titles.
"""

import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.proposal.outline import ProposalOutlineService
from services.proposal.utilities import ProposalUtilities

async def test_toc_generation():
    """Test table of contents generation with the fix applied."""
    
    # Initialize the service
    outline_service = ProposalOutlineService()
    
    # Test parameters
    opportunity_id = "test-opportunity"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "test"
    is_rfp = True
    
    # Get volume information for Volume 2 (Management Approach)
    volume_information = ProposalUtilities.get_volume_information(2)
    
    # Sample content compliance
    content_compliance = """
    Volume II: Management Approach
    
    This volume should demonstrate your management approach including:
    - Past Management Experience
    - Proposed Chain of Command
    - Communication Plan with Government Representatives
    - Remote Management Experience
    - Career Level Management
    - Problem/Issue Addressing capabilities
    """
    
    print("Testing table of contents generation...")
    print("Volume Information:", volume_information[:200] + "..." if len(volume_information) > 200 else volume_information)
    
    try:
        # Generate table of contents
        result = await outline_service.generate_table_of_contents(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            volume_information=volume_information,
            content_compliance=content_compliance,
            is_rfp=is_rfp
        )
        
        print("\n=== TABLE OF CONTENTS GENERATION RESULT ===")
        print(result.get("content", "No content generated"))
        
        # Try to parse the JSON to check titles
        try:
            import json
            content = result.get("content", "")
            if content:
                # Extract JSON from the content
                toc_data = ProposalUtilities.extract_json_from_brackets(content)
                if toc_data and "table_of_contents" in toc_data:
                    print("\n=== PARSED TABLE OF CONTENTS ===")
                    for section in toc_data["table_of_contents"]:
                        title = section.get("title", "")
                        print(f"Section: {title} (Length: {len(title)} characters)")
                        
                        # Check for abbreviations
                        if any(abbrev in title for abbrev in ["Addr.", "Exp.", "Cmd", "Mgmt", "Perf"]):
                            print(f"  ⚠️  WARNING: Title appears to be abbreviated: {title}")
                        else:
                            print(f"  ✅ Title appears to be full: {title}")
                        
                        # Check subsections
                        for subsection in section.get("subsections", []):
                            sub_title = subsection.get("title", "")
                            print(f"  Subsection: {sub_title} (Length: {len(sub_title)} characters)")
                            if any(abbrev in sub_title for abbrev in ["Addr.", "Exp.", "Cmd", "Mgmt", "Perf"]):
                                print(f"    ⚠️  WARNING: Subsection title appears to be abbreviated: {sub_title}")
                            else:
                                print(f"    ✅ Subsection title appears to be full: {sub_title}")
                else:
                    print("Could not parse table of contents from result")
        except Exception as parse_error:
            print(f"Error parsing result: {parse_error}")
        
    except Exception as e:
        print(f"Error generating table of contents: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_toc_generation())
