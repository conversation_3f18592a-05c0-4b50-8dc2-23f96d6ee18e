import re
import asyncio
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController
from services.chroma.chroma_service import ChromaService
from services.proposal.key_personnel import KeyPersonnelService
from services.proposal.research_enhancement_service import ResearchEnhancementService

from database import get_customer_db, get_kontratar_db
from langchain_ollama import ChatOllama
from services.proposal.utilities import ProposalUtilities
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from loguru import logger



def remove_first_markdown_title_regex(text: str) -> str:
    """
    Remove only the first line that starts with ## using regex.
    
    Args:
        text: Input text containing markdown titles
        
    Returns:
        Text with only the first ## title removed
    """
    # Remove only the first line starting with ## (with optional whitespace before ##)
    return re.sub(r'^\s*##.*$', '', text, flags=re.MULTILINE, count=1).strip()


class ProposalOutlineService:
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()

        self.chroma_service = ChromaService(embedding_api_url, None)
        self.key_personnel_service = KeyPersonnelService()
        self.research_enhancement_service = ResearchEnhancementService()
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=6300,
            temperature=0,
            base_url=llm_api_url
        )

    async def generate_chroma_query(self, text: str, is_rfp: bool = True):
        if not text:
            return ""

        llm = ChatOllama(model="gemma3:27b", base_url="http://ai.kontratar.com:11434")
        if is_rfp:
            prompt = (
                "Given the following RFP volume information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, evaluation criteria, and any details relevant to the specific volume described below. "
                "This is for a single volume of a multi-volume RFP.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        else:
            prompt = (
                "Given the following RFI information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, government needs, and any details relevant to the RFI topics described below.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        try:
            response = await asyncio.wait_for(
                asyncio.to_thread(llm.invoke, prompt),
                timeout=120  # 2 minute timeout
            )
            return str(response.content)
        except asyncio.TimeoutError:
            logger.error("LLM invocation timed out for search query generation")
            raise Exception("LLM request timed out")
        except Exception as e:
            logger.error(f"LLM invocation failed for search query generation: {e}")
            raise

    async def get_opportunity(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_opportunity called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = None
        if source == "sam":
            logger.info(f"Searching SAM for notice_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.sam_service.get_by_notice_id(db, opportunity_id)
                logger.info(f"SAM search result: {record}")
                break
        elif source == "ebuy":
            logger.info(f"Searching EBUY for rfq_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.ebuy_service.get_by_rfq_id(db, opportunity_id)
                logger.info(f"EBUY search result: {record}")
                break
        elif source == "custom":
            logger.info(f"Searching CUSTOM for opportunity_id={opportunity_id}")
            async for db in get_customer_db():
                record = await self.custom_service.get_main_info_by_opportunity_id(db, opportunity_id)
                logger.info(f"CUSTOM search result: {record}")
                break
        else:
            logger.error(f"Invalid source type: {source}")
            raise ValueError("Invalid source type")

        if record is None:
            logger.error(f"Error getting opportunity metadata for id={opportunity_id}, source={source}")
            raise ValueError("Error getting opportunity metadata")

        logger.info(f"Returning opportunity record: {record}")
        return record        

    async def generate_table_of_contents(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        volume_information: str,
        content_compliance: str,
        is_rfp: bool
    ) -> Dict[str, Any]:
        '''
        chroma_query = await self.generate_chroma_query(volume_information, is_rfp)

        async for db in get_kontratar_db():
            max_chunks = 1
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            try:
                relevant_chunks = await asyncio.wait_for(
                    self.chroma_service.get_relevant_chunks(db, collection_name, chroma_query, n_results=max_chunks),
                    timeout=30.0  # 30 second timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"ChromaDB timeout for collection {collection_name} - using empty context")
                relevant_chunks = []
            except Exception as e:
                logger.error(f"Error retrieving chunks: {e} - using empty context")
                relevant_chunks = []
            # Clean up newlines and tabs
            toc_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context = "\n".join(toc_context)
            break
        '''

        system_prompt = '''
            **Task:**
            Your task is to generate a table of contents for an RFI or Volume of an RFP.
            You will be given different background information to help you generate this table of contents:
            1. Information on how the RFI or Volume should be structured as well as the terminologies/naming convention to use, 
            this will be found in <structure-compliance>
            2. Information on the content that must been to be seen in the RFI or Volume, this will be found in <content-compliance>
            3. Related information from the vector database, this will be found in <context>
        
            **Important:**
            1. YOU MUST only generate the table of contents for the RFI or RFP Volume passed in <structure-compliance>
            2. If you are building the table of contents for an RFP Volume, only use information relevant information found in <content-compliance>
            3. YOU MUST ADD 3 experiences UNDER DEMONSTRATED EXPERIENCE or PAST PERFORMANCE.
            4. YOU MUST ADD ALL Statement of Work Tasks to the TECHNICAL APPROACH (or similar) with the exact naming. DO NOT leave out ANYTHING from the SOW.
            5. The response SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            6. You will be given a JSON schema to comply to, ensure to follow it strictly.
        '''

        user_prompt = f'''
            Generate the table of contents for this RFI/RFP
            
            <structure-compliance>
                {volume_information}
            </structure-compliance>

            <content-compliance>
                {content_compliance}
            </content-compliance>

            USE ONLY information found in <structure-compliance>, <content-compliance>, and <context> to build the response.

            Use the JSON schema below:
            {{
                "table_of_contents": [
                    {{
                        "title": "string",
                        "description": "string",
                        "number": "string",
                        "subsections": [
                            {{
                                "number": "string",
                                "title": "string",
                                "description": "string"
                            }}
                        ]
                    }}
                ]
            }}

            - "table_of_contents" is an array of sections for the RFI or RFP Volume.
            - "title" is the name of the main section or subsection AND it MUST align with the naming conventions used in <structure-compliance>
            - "title" should be clear, professional, and descriptive. Use full titles without abbreviations unless specifically required by the RFP. Prefer concise but complete titles (aim for 60-80 characters when possible, but prioritize clarity over brevity).
            - "description" MUST be a brief description of the content that must be contained in that section or subsection.
            - "subsections" is an array of subsections for each section.
            - "number" is THE assigned number for a main section or subsection (eg main section 1.0, subsection 1.1.3). "number" MUST
                start from 1.0
        '''

        # LLM call (with retry logic)
        content = None
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        try:
            content = await asyncio.wait_for(
                asyncio.to_thread(self.llm.invoke, messages),
                timeout=300  # 5 minute timeout
            )
        except asyncio.TimeoutError:
            logger.error("LLM invocation timed out for table of contents generation")
            raise Exception("LLM request timed out")
        except Exception as e:
            logger.error(f"LLM invocation failed for table of contents generation: {e}")
            raise

        print(content.content)
        
        return {"content": content.content}

    
    async def generate_outline(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a detailed outline for each section and subsection in the table of contents.
        Each outline contains a title, content, and optionally an array of image descriptions.
        The outlines are nested to match the table of contents hierarchy.

        Enhanced for government standards with:
        - Comprehensive validation and error handling
        - Government-compliant prompt engineering
        - Structured context retrieval
        - Quality assurance checks
        """

        logger.info(f"OUTLINE: Starting enhanced outline generation for opportunity {opportunity_id}")
        logger.info(f"OUTLINE: Processing {len(table_of_contents)} main sections")

        @retry(
            stop=stop_after_attempt(5),
            wait=wait_fixed(3),
            retry=retry_if_exception_type(Exception),
            reraise=True
        )
        async def outline_for_section(section: Dict[str, Any], depth: int = 0) -> Dict[str, Any]:
            """Enhanced section outline generation with government compliance"""
            section_title = section.get("title", "").strip()
            section_desc = section.get("description", "").strip()
            section_number = section.get("number", "").strip()

            indent = "  " * depth
            logger.info(f"OUTLINE: {indent}Processing section {section_number} - {section_title}")

            if not section_title:
                logger.warning(f"OUTLINE: {indent}Empty section title found, skipping")
                return {}

            # Enhanced ChromaDB query strategy
            chroma_queries = [
                f"Requirements and evaluation criteria for {section_title} section",
                f"Content specifications and deliverables for {section_title}",
                f"Government standards and compliance requirements for {section_title}",
                f"Technical approach and methodology requirements for {section_title}"
            ]

            # Fetch comprehensive context with timeout protection
            context_chunks = []
            try:
                async for db in get_kontratar_db():
                    collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                    for query in chroma_queries:
                        try:
                            chunks = await asyncio.wait_for(
                                self.chroma_service.get_relevant_chunks(db, collection_name, query, n_results=2),
                                timeout=30.0
                            )
                            context_chunks.extend(chunks)
                        except asyncio.TimeoutError:
                            logger.warning(f"OUTLINE: {indent}ChromaDB timeout for query: {query[:50]}...")
                        except Exception as e:
                            logger.error(f"OUTLINE: {indent}ChromaDB error for query: {e}")
                    break

            except Exception as e:
                logger.error(f"OUTLINE: {indent}Database connection error: {e}")

            # Process and clean context
            if context_chunks:
                section_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in context_chunks]
                context = "\n".join(section_context)
                logger.info(f"OUTLINE: {indent}Retrieved {len(context_chunks)} context chunks ({len(context)} chars)")
            else:
                context = f"Section: {section_title}\nDescription: {section_desc}"
                logger.warning(f"OUTLINE: {indent}No context retrieved, using fallback")

            # Enhanced government-compliant system prompt
            system_prompt = '''
                **ROLE:** Government Proposal Outline Expert
                **MISSION:** Generate comprehensive, government-compliant proposal section outlines that meet federal evaluation standards.

                **CRITICAL GOVERNMENT COMPLIANCE REQUIREMENTS:**
                1. ZERO placeholders, brackets, TBD, TODO, or incomplete information in any field
                2. NO generic content - everything must be specific to THIS section and requirement
                3. NO repetition of RFP administrative requirements or formatting rules
                4. FOCUS EXCLUSIVELY on demonstrating technical capability for the specific requirement
                5. PROVIDE concrete, actionable guidance that leads to substantive content
                6. ENSURE all guidance aligns with federal proposal evaluation criteria
                7. MANDATE specific methodologies, processes, and measurable outcomes

                **OUTLINE COMPONENTS (ALL REQUIRED):**
                - title: Exact section title from table of contents
                - content: Comprehensive guidance on what to include and what to avoid
                - page_limit: Maximum pages allowed (extract from context or use standard: 2-5 pages)
                - purpose: Primary evaluation purpose (Demonstrate Capability, Show Understanding, Prove Experience, etc.)
                - rfp_vector_db_query: Specific query to retrieve RFP requirements for this section
                - client_vector_db_query: Targeted query to get relevant company capabilities
                - custom_prompt: Detailed, step-by-step content generation instructions
                - references: Exact text from context that supports this outline
                - image_descriptions: Required tables/diagrams (if applicable)

                **CONTENT GUIDANCE STANDARDS:**
                - Specify EXACTLY what technical details to include
                - Define SPECIFIC methodologies and processes to describe
                - Identify MEASURABLE outcomes and success criteria to present
                - Clarify HOW to demonstrate understanding of government requirements
                - Outline CONCRETE examples and case studies to reference
                - Specify COMPLIANCE requirements and standards to address

                **CUSTOM PROMPT REQUIREMENTS:**
                - Include step-by-step content creation instructions
                - Specify required technical depth and detail level
                - Mandate specific government terminology and naming conventions
                - Define required structure (headers, bullets, tables)
                - Include quality checkpoints and validation criteria
                - Specify word count and page limit compliance

                **MANDATORY TABLE/DIAGRAM IDENTIFICATION:**
                - Staffing Plan: Role/Responsibilities/Qualifications table
                - Technical Approach: Process flow diagrams, methodology tables
                - Past Performance: Project summary tables
                - Management Plan: Organizational charts, timeline tables
                - Quality Assurance: QA process diagrams, metrics tables

                **JSON COMPLIANCE:**
                - Return ONLY valid JSON - no explanatory text
                - Follow the exact schema provided
                - Ensure all string fields are properly escaped
                - Validate all required fields are present
            '''

            # Enhanced user prompt with structured context
            user_prompt = f'''
                **SECTION ANALYSIS:**
                Section Number: {section_number}
                Section Title: {section_title}
                Section Description: {section_desc}

                **RFP CONTEXT:**
                {context[:2000] if context else "No specific RFP context available"}

                **REQUIREMENTS:**
                Generate a comprehensive outline that enables creation of government-compliant content.
                Focus on the specific requirements and evaluation criteria for this section.

                **MANDATORY JSON SCHEMA:**
                {{
                    "title": "string - exact section title",
                    "content": "string - detailed guidance on what to include/exclude (minimum 200 words)",
                    "page_limit": number - maximum pages (2-5 typical),
                    "purpose": "string - primary evaluation purpose",
                    "rfp_vector_db_query": "string - specific query for RFP requirements",
                    "client_vector_db_query": "string - targeted query for company capabilities",
                    "custom_prompt": "string - step-by-step content generation instructions (minimum 300 words)",
                    "references": "string - exact text from context supporting this outline",
                    "image_descriptions": ["string"] - required tables/diagrams (if applicable)
                }}

                **VALIDATION REQUIREMENTS:**
                - All fields must be complete and substantive
                - No placeholders or generic content
                - Custom prompt must include specific government terminology
                - Content guidance must be actionable and specific
                - References must be exact quotes from provided context
            '''

            # Enhanced LLM call with comprehensive validation
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"OUTLINE: {indent}LLM attempt {attempt + 1}/{max_attempts} for {section_title}")

                    messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ]
                    try:
                        result = await asyncio.wait_for(
                            asyncio.to_thread(self.llm.invoke, messages),
                            timeout=300  # 5 minute timeout
                        )
                        content = str(result.content).strip()
                    except asyncio.TimeoutError:
                        logger.error(f"OUTLINE: {indent}LLM timeout on attempt {attempt + 1} for {section_title}")
                        if attempt == max_attempts - 1:
                            raise Exception("LLM request timed out")
                        continue

                    if not content:
                        logger.warning(f"OUTLINE: {indent}Empty LLM response on attempt {attempt + 1}")
                        continue

                    # Enhanced JSON extraction with validation
                    outline = ProposalUtilities.extract_json_from_brackets(content)

                    if outline is None:
                        logger.warning(f"OUTLINE: {indent}Failed to extract JSON on attempt {attempt + 1}")
                        logger.debug(f"OUTLINE: {indent}Raw content: {content[:200]}...")
                        continue

                    # Validate required fields and content quality
                    validation_errors = self._validate_outline_quality(outline, section_title, indent)

                    if validation_errors:
                        logger.warning(f"OUTLINE: {indent}Validation errors on attempt {attempt + 1}: {validation_errors}")
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            # Use outline with warnings for final attempt
                            logger.warning(f"OUTLINE: {indent}Using outline with validation warnings")

                    logger.info(f"OUTLINE: {indent}Successfully generated outline for {section_title}")

                    # Recursively process subsections with depth tracking
                    subsections = section.get("subsections", [])
                    if subsections:
                        outline["subsections"] = []
                        logger.info(f"OUTLINE: {indent}Processing {len(subsections)} subsections")
                        for subsection in subsections:
                            sub_outline = await outline_for_section(subsection, depth + 1)
                            if sub_outline:  # Only add non-empty subsections
                                outline["subsections"].append(sub_outline)

                    return outline

                except Exception as e:
                    logger.error(f"OUTLINE: {indent}Error on attempt {attempt + 1}: {e}")
                    if attempt == max_attempts - 1:
                        raise
                    continue

            # Fallback if all attempts failed
            logger.error(f"OUTLINE: {indent}All attempts failed for {section_title}")
            return {}

        # Build the enhanced nested outline structure with comprehensive tracking
        outlines = []
        total_sections = len(table_of_contents)
        successful_sections = 0

        for i, section in enumerate(table_of_contents, 1):
            logger.info(f"OUTLINE: Processing main section {i}/{total_sections}: {section.get('title', 'Unknown')}")

            try:
                outline = await outline_for_section(section, depth=0)
                if outline:
                    outlines.append(outline)
                    successful_sections += 1
                    logger.info(f"OUTLINE: Successfully processed section {i}/{total_sections}")
                else:
                    logger.warning(f"OUTLINE: Empty outline returned for section {i}/{total_sections}")
            except Exception as e:
                logger.error(f"OUTLINE: Failed to process section {i}/{total_sections}: {e}")
                # Continue with other sections rather than failing completely
                continue

        # Generate comprehensive summary
        logger.info(f"OUTLINE: Generation complete - {successful_sections}/{total_sections} sections successful")

        return {
            "outlines": outlines,
            "generation_summary": {
                "total_sections": total_sections,
                "successful_sections": successful_sections,
                "success_rate": (successful_sections / total_sections * 100) if total_sections > 0 else 0,
                "enhanced_features": [
                    "Government compliance validation",
                    "Multi-query context retrieval",
                    "Comprehensive error handling",
                    "Quality assurance checks"
                ]
            }
        }

    def _validate_outline_quality(self, outline: Dict[str, Any], section_title: str, indent: str = "") -> List[str]:
        """Validate outline quality against government standards"""
        errors = []

        # Required fields validation
        required_fields = ["title", "content", "page_limit", "purpose", "rfp_vector_db_query",
                          "client_vector_db_query", "custom_prompt", "references"]

        for field in required_fields:
            if field not in outline:
                errors.append(f"Missing required field: {field}")
            elif not outline[field] or (isinstance(outline[field], str) and len(outline[field].strip()) < 10):
                errors.append(f"Field '{field}' is empty or too short")

        # Content quality validation
        if "content" in outline:
            content = outline["content"]
            if len(content) < 200:
                errors.append("Content guidance too short (minimum 200 characters)")
            if any(placeholder in content.lower() for placeholder in ["[", "]", "tbd", "todo", "placeholder"]):
                errors.append("Content contains placeholders or brackets")

        # Custom prompt validation
        if "custom_prompt" in outline:
            prompt = outline["custom_prompt"]
            if len(prompt) < 300:
                errors.append("Custom prompt too short (minimum 300 characters)")
            if "step" not in prompt.lower():
                errors.append("Custom prompt missing step-by-step instructions")

        # Page limit validation
        if "page_limit" in outline:
            try:
                page_limit = int(outline["page_limit"])
                if page_limit < 1 or page_limit > 20:
                    errors.append("Page limit should be between 1-20 pages")
            except (ValueError, TypeError):
                errors.append("Page limit must be a valid number")

        if errors:
            logger.warning(f"OUTLINE: {indent}Quality validation failed for {section_title}: {errors}")

        return errors

    def _process_draft_content(self, content: str, is_cover_letter: bool, section_title: str, indent: str = "") -> str:
        """Enhanced content processing that preserves cover letter formatting"""
        try:
            # Remove first markdown title if present
            text = remove_first_markdown_title_regex(content)
            text = text.strip()

            if is_cover_letter:
                # For cover letters, preserve formatting and only remove markdown code blocks
                logger.info(f"DRAFT: {indent}Processing cover letter content (preserving formatting)")

                # Only remove markdown code blocks, preserve other formatting
                if text.startswith('```'):
                    first_newline = text.find('\n')
                    if first_newline != -1:
                        text = text[first_newline + 1:]

                if text.endswith('```'):
                    text = text[:-3]

                # Remove only markdown code block markers, preserve other content
                text = text.replace('```markdown', '').replace('```', '')
                text = text.strip()

                # Preserve line breaks and formatting for cover letters
                logger.info(f"DRAFT: {indent}Cover letter processed, length: {len(text)} chars")

            else:
                # For technical sections, apply standard processing
                logger.info(f"DRAFT: {indent}Processing technical section content")

                if text.startswith('```'):
                    first_newline = text.find('\n')
                    if first_newline != -1:
                        text = text[first_newline + 1:]

                if text.endswith('```'):
                    text = text[:-3]
                text = text.replace('```', '')
                text = text.strip()

                logger.info(f"DRAFT: {indent}Technical section processed, length: {len(text)} chars")

            return text

        except Exception as e:
            logger.error(f"DRAFT: {indent}Error processing content: {e}")
            return content.strip()

    def _validate_draft_quality(self, content: str, section_title: str, is_cover_letter: bool, indent: str = "") -> List[str]:
        """Validate draft content quality against government standards"""
        errors = []

        try:
            # Basic content validation
            if len(content.strip()) < 50:
                errors.append("Content too short (minimum 50 characters)")

            placeholder_patterns = ["[", "]", "tbd", "todo", "placeholder", "xxx", "yyy"]
            if not is_cover_letter:
                # Stricter validation for technical sections
                for pattern in placeholder_patterns:
                    if pattern in content.lower():
                        errors.append(f"Content contains placeholder: {pattern}")
            else:
                critical_placeholders = ["[replace", "[insert", "tbd", "todo", "xxx"]
                for pattern in critical_placeholders:
                    if pattern in content.lower():
                        errors.append(f"Cover letter contains placeholder: {pattern}")

            generic_phrases = ["lorem ipsum", "sample text", "example content", "generic"]
            for phrase in generic_phrases:
                if phrase in content.lower():
                    errors.append(f"Content contains generic phrase: {phrase}")

            if is_cover_letter:
                required_elements = ["dear", "sincerely", "regards"]
                found_elements = sum(1 for element in required_elements if element in content.lower())
                if found_elements < 2:
                    errors.append("Cover letter missing required formal elements")

                if len(content) < 200:
                    errors.append("Cover letter too short (minimum 200 characters)")
            else:
                if len(content) < 100:
                    errors.append("Technical content too short (minimum 100 characters)")

                # More intelligent relevance checking
                title_words = section_title.lower().split()
                content_lower = content.lower()

                # Filter out common words and section identifiers
                meaningful_words = [
                    word for word in title_words
                    if len(word) > 3 and word not in [
                        'tab', 'factor', 'section', 'part', 'volume', 'appendix',
                        'attachment', 'exhibit', 'schedule', 'item', 'element'
                    ]
                ]

                if meaningful_words:  # Only check if there are meaningful words
                    relevant_words = sum(1 for word in meaningful_words if word in content_lower)
                    # More lenient threshold - at least one meaningful word should appear
                    if relevant_words == 0 and len(meaningful_words) > 2:
                        errors.append("Content may not be sufficiently relevant to section title")

            if errors:
                logger.warning(f"DRAFT: {indent}Quality validation failed for {section_title}: {errors}")

        except Exception as e:
            logger.error(f"DRAFT: {indent}Error during validation: {e}")
            errors.append(f"Validation error: {str(e)}")

        return errors

    def _is_key_personnel_section_fast(self, title: str, description: str) -> tuple[bool, bool]:
        """
        Fast personnel detection using keyword matching first, then determining if LLM is needed.
        Returns (is_personnel, needs_llm_verification)
        """
        title_lower = title.lower()
        desc_lower = description.lower()

        # Strong positive indicators - definitely personnel sections
        strong_personnel_keywords = [
            'key personnel', 'personnel', 'staff', 'team', 'qualifications', 'resumes',
            'cv', 'curriculum vitae', 'biography', 'biographies', 'staffing',
            'organizational chart', 'project manager', 'technical lead', 'labor categories',
            'employee', 'contractor', 'consultant', 'specialist', 'expert'
        ]

        # Strong negative indicators - definitely NOT personnel sections
        strong_non_personnel_keywords = [
            'technical approach', 'methodology', 'solution', 'tool', 'software',
            'pricing', 'cost', 'budget', 'schedule', 'timeline', 'risk management',
            'quality assurance', 'testing', 'deployment', 'infrastructure',
            'architecture', 'design', 'implementation', 'maintenance'
        ]

        # Check for strong positive matches
        for keyword in strong_personnel_keywords:
            if keyword in title_lower or keyword in desc_lower:
                logger.info(f"Fast personnel detection (POSITIVE): '{title}' - matched '{keyword}'")
                return True, False

        # Check for strong negative matches
        for keyword in strong_non_personnel_keywords:
            if keyword in title_lower:
                logger.info(f"Fast personnel detection (NEGATIVE): '{title}' - matched '{keyword}'")
                return False, False

        logger.info(f"Fast personnel detection (AMBIGUOUS): '{title}' - needs LLM verification")
        return False, True

    async def _is_key_personnel_section(self, title: str, description: str) -> bool:
        """
        Optimized personnel detection with fast keyword matching + selective LLM verification.
        """
        is_personnel, needs_llm = self._is_key_personnel_section_fast(title, description)

        if not needs_llm:
            return is_personnel

        logger.info(f"Using LLM for ambiguous personnel detection: '{title}'")

        system_prompt = """
You are an expert at analyzing government proposal sections. Your task is to determine if a proposal section is about key personnel, staff, team members, or qualifications.

Return ONLY "true" or "false" - nothing else.

A section is about key personnel if it involves:
- Staff qualifications and experience
- Team member profiles or resumes
- Personnel assignments or roles
- Key person biographies or credentials
- Staffing plans or organizational charts
- Employee qualifications or certifications
- Project team composition
- Management team descriptions
- Technical staff expertise
- Labor categories or skill sets

A section is NOT about key personnel if it's about:
- Technical approaches or methodologies
- Project management processes
- Company capabilities or past performance
- Technical solutions or tools
- Pricing or cost proposals
- Schedules or timelines
- Risk management
- Quality assurance processes
"""

        user_prompt = f"""
Section Title: {title}
Section Description: {description}

Is this section about key personnel, staff, or team qualifications?
"""

        try:
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            try:
                result = await asyncio.wait_for(
                    asyncio.to_thread(self.llm.invoke, messages),
                    timeout=120  # 2 minute timeout
                )
                response = str(result.content).strip().lower()
            except asyncio.TimeoutError:
                logger.error(f"LLM invocation timed out for personnel detection: {title}")
                raise Exception("LLM request timed out")

            # Parse the response
            is_personnel = response == "true" or "true" in response

            logger.info(f"LLM personnel detection for '{title}': {is_personnel} (response: {response})")
            return is_personnel

        except Exception as e:
            logger.error(f"Error in LLM personnel detection: {e}")
            # Fallback to keyword matching if LLM fails
            keywords = ['key personnel', 'personnel', 'staff', 'team', 'qualifications', 'resumes']
            fallback_result = any(keyword in title.lower() for keyword in keywords)
            logger.info(f"Using fallback keyword detection: {fallback_result}")
            return fallback_result


    ## Enhanced draft generation with government standards
    async def generate_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft for each section and subsection in the table of contents.
        The drafts are nested to match the table of contents hierarchy.
        """

        logger.info(f"DRAFT: Starting enhanced draft generation for opportunity {opportunity_id}")
        logger.info(f"DRAFT: Processing {len(table_of_contents)} main sections")

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        personnel_cache = {}
        personnel_sections = []

        def collect_personnel_sections(sections, depth=0):
            for section in sections:
                section_title = section.get("title", "").strip()
                section_desc = section.get("description", "").strip()

                if section_title:
                    is_personnel, needs_llm = self._is_key_personnel_section_fast(section_title, section_desc)
                    if is_personnel or needs_llm:
                        personnel_sections.append({
                            'section': section,
                            'is_personnel': is_personnel,
                            'needs_llm': needs_llm,
                            'depth': depth
                        })

                if section.get("subsections"):
                    collect_personnel_sections(section["subsections"], depth + 1)

        collect_personnel_sections(table_of_contents)

        if personnel_sections:
            logger.info(f"DRAFT: Pre-fetching personnel data for {len(personnel_sections)} potential personnel sections")
            try:
                personnel_data = await self.key_personnel_service.generate_key_personnel_content(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    section_requirements="General key personnel requirements"
                )
                personnel_cache['data'] = personnel_data
                personnel_cache['has_data'] = personnel_data.get("has_real_resumes", False)
                logger.info(f"DRAFT: Personnel data cached - has_real_resumes: {personnel_cache['has_data']}")
            except Exception as e:
                logger.error(f"DRAFT: Error pre-fetching personnel data: {e}")
                personnel_cache['data'] = None
                personnel_cache['has_data'] = False

        @retry(
            stop=stop_after_attempt(5),
            wait=wait_fixed(3),
            retry=retry_if_exception_type(Exception),
            reraise=True
        )
        async def draft_for_section(section: Dict[str, Any], depth: int = 0) -> Dict[str, Any]:
            """Enhanced section draft generation with government compliance and cover letter handling"""
            section_title = section.get("title", "").strip()
            section_desc = section.get("description", "").strip()
            section_number = section.get("number", "").strip()

            indent = "  " * depth
            logger.info(f"DRAFT: {indent}Processing section {section_number} - {section_title}")

            if not section_title:
                logger.warning(f"DRAFT: {indent}Empty section title found, skipping")
                return {}

            is_cover_letter = any(keyword in section_title.lower() for keyword in [
                'cover', 'transmittal', 'letter', 'introduction letter', 'proposal letter'
            ])

            logger.info(f"DRAFT: {indent}Cover letter detected: {is_cover_letter}")

            # Enhanced context retrieval with multiple targeted queries
            context = ""
            client_context_str = ""

            try:
                async for db in get_kontratar_db():
                    opportunity_collection = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                    # Multiple targeted queries for better context
                    chroma_queries = [
                        f"Requirements and specifications for {section_title} section",
                        f"Content and deliverables expected in {section_title}",
                        f"Evaluation criteria and standards for {section_title}"
                    ]

                    all_chunks = []
                    for query in chroma_queries:
                        try:
                            chunks = await asyncio.wait_for(
                                self.chroma_service.get_relevant_chunks(db, opportunity_collection, query, n_results=2),
                                timeout=30.0
                            )
                            all_chunks.extend(chunks)
                        except asyncio.TimeoutError:
                            logger.warning(f"DRAFT: {indent}ChromaDB timeout for query: {query[:50]}...")
                        except Exception as e:
                            logger.error(f"DRAFT: {indent}ChromaDB error: {e}")

                    if all_chunks:
                        rfp_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in all_chunks]
                        context = "\n".join(rfp_context)
                        logger.info(f"DRAFT: {indent}Retrieved {len(all_chunks)} RFP context chunks")

                    # Client context retrieval
                    client_collection = f"{tenant_id}_{client_short_name}"
                    client_query = f"Company capabilities and experience relevant to {section_title}"

                    try:
                        client_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, client_collection, client_query, n_results=3),
                            timeout=30.0
                        )
                        client_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in client_chunks]
                        client_context_str = "\n".join(client_context)
                        logger.info(f"DRAFT: {indent}Retrieved {len(client_chunks)} client context chunks")
                    except asyncio.TimeoutError:
                        logger.warning(f"DRAFT: {indent}Client context timeout")
                    except Exception as e:
                        logger.error(f"DRAFT: {indent}Client context error: {e}")

                    break

            except Exception as e:
                logger.error(f"DRAFT: {indent}Database connection error: {e}")
                context = f"Section: {section_title}\nDescription: {section_desc}"
                client_context_str = "Professional services company with government contracting expertise."

            is_key_personnel = await self._is_key_personnel_section(section_title, section_desc)

            personnel_context = ""
            if is_key_personnel:
                logger.info(f"DRAFT: {indent}Key personnel section detected, using cached data")

                if personnel_cache.get('has_data', False):
                    cached_data = personnel_cache['data']
                    personnel_context = f"\n\nREAL PERSONNEL DATA:\n{cached_data['content']}"
                    logger.info(f"DRAFT: {indent}Using cached real resume data for {cached_data['personnel_count']} personnel")
                elif personnel_cache.get('data'):
                    logger.warning(f"DRAFT: {indent}No real resume data available, will generate based on requirements only")
                    personnel_context = f"\n\nPERSONNEL REQUIREMENTS: Generate key personnel section based on the requirements in the section description. Do not use placeholder names or fabricated information."
                else:
                    logger.warning(f"DRAFT: {indent}No personnel data available in cache")

            # # Enhance content with academic research for technical sections
            # research_context = ""
            # if not is_cover_letter and not is_key_personnel:
            #     logger.info(f"DRAFT: {indent}Enhancing section with academic research")
            #     try:
            #         research_enhancement = await self.research_enhancement_service.enhance_section_with_research(
            #             section_title=section_title,
            #             section_content=f"{section_desc}\n{context}",
            #             opportunity_context=getattr(record, 'description', ''),
            #             max_papers=3
            #         )

            #         if research_enhancement.get("research_added", False):
            #             research_context = f"\n\nRESEARCH ENHANCEMENT:\n{research_enhancement['enhancement_summary']}"
            #             logger.info(f"DRAFT: {indent}Added research from {research_enhancement['papers_found']} papers")

            #     except Exception as e:
            #         logger.error(f"DRAFT: {indent}Error enhancing with research: {e}")

            if is_cover_letter:
                system_prompt = '''
                    **ROLE:** Government Proposal Cover Letter Expert
                    **MISSION:** Generate professional, compliant cover letters that meet federal proposal standards.

                    **CRITICAL COVER LETTER REQUIREMENTS:**
                    1. ZERO placeholders, brackets, TBD, TODO, or incomplete information
                    2. INCLUDE proper business letter format with date, addresses, and signatures
                    3. EXTRACT real company information from provided metadata - NO fabrication
                    4. REFERENCE specific opportunity details
                    5. DEMONSTRATE understanding of the government requirement
                    6. MAINTAIN professional, formal tone throughout
                    7. INCLUDE proper contact information and company credentials
                    8. COMPLY with standard business letter formatting

                    **COVER LETTER STRUCTURE:**
                    - Proper letterhead with company information
                    - Current date
                    - Government agency address (if available)
                    - Reference line with opportunity title
                    - Professional salutation
                    - Opening paragraph stating purpose and company interest
                    - Body paragraph highlighting relevant qualifications
                    - Closing paragraph expressing commitment and next steps
                    - Professional closing with signature block
                    - Contact information

                    **CONTENT STANDARDS:**
                    - Use specific company capabilities from metadata
                    - Reference actual opportunity requirements
                    - Demonstrate understanding of government needs
                    - Maintain professional, confident tone
                    - Include relevant certifications or credentials
                    - Show commitment to compliance and quality
                '''
            else:
                system_prompt = '''
                    **ROLE:** Government Proposal Technical Content Expert
                    **MISSION:** Generate professional government proposal content with 100% compliance.

                    **CRITICAL REQUIREMENTS:**
                    1. ZERO placeholders, brackets, TBD, TODO, or incomplete information
                    2. NO contact info in technical sections
                    3. NO repetition of RFP requirements or administrative details
                    4. NO generic marketing language - be specific and evidence-based
                    5. FOCUS EXCLUSIVELY on demonstrating capability for the requested work
                    6. USE concrete methodologies and measurable outcomes
                    7. WRITE for government evaluators assessing technical capability
                    8. COMPLY with page limits and formatting requirements
                    9. NO repetitive phrases like "detailed qualifications repeated from prior response"
                    10. NO meta-commentary about the content being generated
                    11. If personnel data is provided, use ONLY that data - NO fabricated names or details

                    **CONTENT STANDARDS:**
                    - Demonstrate HOW you will perform the work with specific processes
                    - Show deep understanding of government requirements
                    - Provide specific methodologies, tools, and success criteria
                    - Focus on capability demonstration, not company marketing
                    - Use professional, direct language
                    - Structure content for easy evaluation (headers, bullets, tables)
                    - Include specific metrics, timelines, and deliverables
                    - Keep paragraphs concise (3-5 sentences maximum)

                    **FORMATTING STANDARDS:**
                    - Use clear section headers and subheaders
                    - Include bullet points for key information
                    - Generate relevant tables in markdown when they support capability demonstration
                    - Tables must be complete with specific data - NO placeholders
                    - Use active voice and strong action verbs
                    - Ensure content is scannable and easy to evaluate
                '''

            if is_cover_letter:
                opportunity_title = getattr(record, 'title', 'Government Consulting Services')
                opportunity_description = getattr(record, 'description', 'Professional services opportunity')

                # Generate current date
                from datetime import datetime
                current_date = datetime.now().strftime("%B %d, %Y")

                # Check if this is a specific type of letter
                title_lower = section_title.lower()
                is_tentative_letter = 'tentative' in title_lower
                is_contingent_letter = 'contingent' in title_lower
                is_offer_letter = 'offer letter' in title_lower and ('tentative' in title_lower or 'contingent' in title_lower)

                if is_tentative_letter or is_contingent_letter or is_offer_letter:
                    user_prompt = f'''
                        Generate a formal tentative/contingent offer letter for employment using the company information provided.

                        **CRITICAL REQUIREMENTS:**
                        - This is NOT a cover letter - it's a conditional job offer letter
                        - NO placeholders, brackets, or [Replace with...] text
                        - NO markdown code blocks or ``` formatting
                        - Extract company information from the tenant metadata provided
                        - Use proper business letter format for employment offers
                        - Include specific conditional terms and requirements
                        - NO fabricated or hallucinated information

                        **LETTER TYPE:** {"Tentative" if is_tentative_letter else "Contingent"} Offer Letter

                        **OPPORTUNITY INFORMATION:**
                        - Date: {current_date}
                        - Position/Project: {opportunity_title}
                        - Description: {opportunity_description}

                        **COMPANY INFORMATION:**
                        {tenant_metadata}

                        **CONTEXT:**
                        {context[:1000] if context else "No specific context available"}

                        **FORMAT REQUIREMENTS:**
                        Generate a complete conditional offer letter with:
                        1. Company letterhead information
                        2. Current date
                        3. Candidate/recipient address placeholder
                        4. Subject line indicating tentative/contingent offer
                        5. Professional salutation
                        6. Opening paragraph extending the conditional offer
                        7. Clear list of conditions that must be met
                        8. Expected start date and compensation details
                        9. Statement about conditional nature of offer
                        10. Instructions for acceptance
                        11. Professional closing with signature block
                        12. Acceptance signature section

                        **REQUIRED CONDITIONS TO INCLUDE:**
                        - Background verification including employment history, education, and criminal record check
                        - Reference checks deemed satisfactory by the team
                        - Verification of eligibility to work
                        - Confirmation of project funding/contract approval (if applicable)

                        Extract the company name, hiring manager, and contact details from the tenant metadata.
                    '''
                else:
                    # Standard cover letter prompt
                    user_prompt = f'''
                        Generate a formal government proposal cover letter using the company information provided.

                    **CRITICAL REQUIREMENTS:**
                    - NO placeholders, brackets, or [Replace with...] text
                    - NO markdown code blocks or ``` formatting
                    - Extract company information from the tenant metadata provided
                    - Use proper business letter format
                    - NO fabricated or hallucinated information
                    - Include proper contact information and company credentials

                    **OPPORTUNITY INFORMATION:**
                    - Date: {current_date}
                    - Opportunity: {opportunity_title}
                    - Description: {opportunity_description}

                    **COMPANY INFORMATION:**
                    {tenant_metadata}

                    **RFP CONTEXT:**
                    {context[:1000] if context else "No specific RFP context available"}

                    **FORMAT REQUIREMENTS:**
                    Generate a complete business letter with:
                    1. Company letterhead information
                    2. Current date
                    3. Government agency address (if available in context)
                    4. Reference line with opportunity title
                    5. Professional salutation
                    6. Opening paragraph stating purpose
                    7. Body paragraph highlighting relevant qualifications
                    8. Closing paragraph expressing commitment
                    9. Professional closing with signature block
                    10. Contact information

                    Extract the company name, contact person, and contact details from the tenant metadata.
                    '''
            else:
                user_prompt = f'''
                    Generate a {section_title} section for a government proposal.

                    **REQUIREMENTS:**
                    - Return ONLY the section content - NO titles or explanations
                    - NO placeholders, brackets, TBD, TODO, or incomplete information
                    - NO contact info, generic marketing, or RFP repetition
                    - Demonstrate HOW you will perform the work with specific processes
                    - Use concrete methodologies and measurable outcomes
                    - Comply with page limits (typically 2-5 pages)
                    - Use clear structure with headers and bullet points where appropriate

                    **SECTION DESCRIPTION:**
                    {section_desc}

                    **RFP CONTEXT:**
                    {context[:1500] if context else "No context provided"}

                    **COMPANY INFORMATION:**
                    {tenant_metadata[:800] if tenant_metadata else ""}
                    {client_context_str[:800] if client_context_str else ""}
                    {personnel_context if personnel_context else ""}

                    Generate professional content demonstrating specific technical capability for this exact requirement.
                '''

            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"DRAFT: {indent}LLM attempt {attempt + 1}/{max_attempts} for {section_title}")

                    messages = [
                        ("system", system_prompt),
                        ("human", user_prompt)
                    ]

                    try:
                        result = await asyncio.wait_for(
                            asyncio.to_thread(self.llm.invoke, messages),
                            timeout=120.0  # 2 minute timeout
                        )
                        content = str(result.content).strip()
                    except asyncio.TimeoutError:
                        logger.error(f"DRAFT: {indent}LLM timeout on attempt {attempt + 1} for {section_title}")
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            # Generate fallback content
                            content = f"Section content for {section_title}. {section_desc}"

                    if not content:
                        logger.warning(f"DRAFT: {indent}Empty LLM response on attempt {attempt + 1}")
                        continue

                    text = self._process_draft_content(content, is_cover_letter, section_title, indent)

                    if not text or len(text.strip()) < 50:
                        logger.warning(f"DRAFT: {indent}Insufficient content on attempt {attempt + 1}")
                        if attempt < max_attempts - 1:
                            continue

                    validation_errors = self._validate_draft_quality(text, section_title, is_cover_letter, indent)

                    if validation_errors:
                        logger.warning(f"DRAFT: {indent}Validation errors on attempt {attempt + 1}: {validation_errors}")

                        critical_errors = [err for err in validation_errors if
                                         'placeholder' in err.lower() or
                                         'too short' in err.lower() or
                                         'generic phrase' in err.lower()]

                        if critical_errors and attempt < max_attempts - 1:
                            logger.info(f"DRAFT: {indent}Retrying due to critical errors: {critical_errors}")
                            continue
                        elif attempt < max_attempts - 1 and len(validation_errors) > 2:
                            # Only retry if multiple serious issues
                            logger.info(f"DRAFT: {indent}Retrying due to multiple validation issues")
                            continue
                        else:
                            logger.warning(f"DRAFT: {indent}Using draft with validation warnings (final attempt or minor issues)")

                    logger.info(f"DRAFT: {indent}Successfully generated draft for {section_title} ({len(text)} chars)")

                    draft = {
                        "title": f"{section_number} {section_title}".strip(),
                        "content": text,
                        "number": section_number,
                        "is_cover_letter": is_cover_letter,
                        "content_length": len(text),
                        "validation_passed": len(validation_errors) == 0
                    }

                    # Recursively process subsections with depth tracking
                    subsections = section.get("subsections", [])
                    if subsections:
                        draft["subsections"] = []
                        logger.info(f"DRAFT: {indent}Processing {len(subsections)} subsections")
                        for subsection in subsections:
                            sub_draft = await draft_for_section(subsection, depth + 1)
                            if sub_draft:  # Only add non-empty subsections
                                draft["subsections"].append(sub_draft)

                    return draft

                except Exception as e:
                    logger.error(f"DRAFT: {indent}Error on attempt {attempt + 1}: {e}")
                    if attempt == max_attempts - 1:
                        raise
                    continue

            # Fallback if all attempts failed
            logger.error(f"DRAFT: {indent}All attempts failed for {section_title}")
            return {}

        # Build the enhanced nested draft structure with comprehensive tracking
        drafts = []
        total_sections = len(table_of_contents)
        successful_sections = 0
        cover_letters_generated = 0

        for i, section in enumerate(table_of_contents, 1):
            logger.info(f"DRAFT: Processing main section {i}/{total_sections}: {section.get('title', 'Unknown')}")

            try:
                draft = await draft_for_section(section, depth=0)
                if draft:
                    drafts.append(draft)
                    successful_sections += 1
                    if draft.get('is_cover_letter', False):
                        cover_letters_generated += 1
                    logger.info(f"DRAFT: Successfully processed section {i}/{total_sections}")
                else:
                    logger.warning(f"DRAFT: Empty draft returned for section {i}/{total_sections}")
            except Exception as e:
                logger.error(f"DRAFT: Failed to process section {i}/{total_sections}: {e}")
                # Continue with other sections rather than failing completely
                continue

        # Generate comprehensive summary
        logger.info(f"DRAFT: Generation complete - {successful_sections}/{total_sections} sections successful")
        logger.info(f"DRAFT: Cover letters generated: {cover_letters_generated}")

        return {
            "draft": drafts
        }


    async def generate_draft_multi_agent(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft using the multi-agent system.

        This method replaces the traditional generate_draft with a sophisticated
        multi-agent approach that provides:
        - Robust LLM handling with intelligent retries
        - Clean failure handling (no fallback content)
        - Specialized agents for different content types
        - Quality assurance and compliance checking

        Args:
            opportunity_id: Unique identifier for the opportunity
            tenant_id: Tenant identifier
            source: Source of the opportunity (custom, sam, etc.)
            client_short_name: Short name of the client
            tenant_metadata: Metadata about the tenant
            table_of_contents: List of sections to generate

        Returns:
            Dictionary containing generated draft with multi-agent metadata
        """

        logger.info(f"MULTI-AGENT: Starting multi-agent draft generation for opportunity {opportunity_id}")
        logger.info(f"MULTI-AGENT: Processing {len(table_of_contents)} sections with specialized agents")

        try:
            # Import the multi-agent workflow
            from services.proposal.multi_agent.workflow import MultiAgentWorkflow

            # Initialize the workflow
            workflow = MultiAgentWorkflow()

            # Validate workflow readiness
            readiness = await workflow.validate_workflow_readiness()
            if not readiness['ready']:
                logger.error(f"MULTI-AGENT: Workflow not ready: {readiness['issues']}")
                raise Exception(f"Multi-agent workflow not ready: {readiness['issues']}")

            logger.info(f"MULTI-AGENT: Workflow ready with {len(readiness['agent_status'])} agents")

            # Generate content for each section using multi-agent system
            draft_sections = {}
            generation_summary = {
                'total_sections': len(table_of_contents),
                'successful_sections': 0,
                'failed_sections': 0,
                'agent_performance': {},
                'quality_scores': []
            }

            for section in table_of_contents:
                section_title = section.get('title', 'Untitled Section')
                section_content = section.get('description', '') + '\n' + section.get('content', '')
                section_type = self._map_section_to_agent_type(section_title)

                logger.info(f"MULTI-AGENT: Generating section '{section_title}' using {section_type} approach")

                try:
                    # Generate content using multi-agent workflow
                    result = await workflow.generate_section_content(
                        opportunity_id=opportunity_id,
                        tenant_id=tenant_id,
                        section_type=section_type,
                        section_content=section_content,
                        client_short_name=client_short_name
                    )

                    if result['success']:
                        logger.info(f"MULTI-AGENT: Section '{section_title}' generated successfully")
                        logger.info(f"MULTI-AGENT: Quality score: {result.get('quality_score', 'N/A')}")

                        # Store the generated content
                        draft_sections[section_title] = {
                            'title': section_title,
                            'content': result['content'],
                            'quality_score': result.get('quality_score'),
                            'agent_metadata': result.get('workflow_summary', {}),
                            'generation_method': 'multi_agent'
                        }

                        generation_summary['successful_sections'] += 1
                        if result.get('quality_score'):
                            generation_summary['quality_scores'].append(result['quality_score'])

                        # Track agent performance
                        for agent_role, agent_result in result.get('agent_results', {}).items():
                            if agent_role not in generation_summary['agent_performance']:
                                generation_summary['agent_performance'][agent_role] = {
                                    'successes': 0, 'failures': 0, 'total_time': 0
                                }

                            if agent_result.get('success'):
                                generation_summary['agent_performance'][agent_role]['successes'] += 1
                            else:
                                generation_summary['agent_performance'][agent_role]['failures'] += 1

                            generation_summary['agent_performance'][agent_role]['total_time'] += agent_result.get('processing_time', 0)

                    else:
                        logger.error(f"MULTI-AGENT: Section '{section_title}' generation failed: {result.get('error')}")
                        generation_summary['failed_sections'] += 1

                        # NO FALLBACK CONTENT - fail cleanly
                        draft_sections[section_title] = {
                            'title': section_title,
                            'content': None,
                            'error': result.get('error', 'Unknown error'),
                            'generation_method': 'multi_agent_failed'
                        }

                except Exception as e:
                    logger.error(f"MULTI-AGENT: Exception generating section '{section_title}': {e}")
                    generation_summary['failed_sections'] += 1

                    # NO FALLBACK CONTENT - fail cleanly
                    draft_sections[section_title] = {
                        'title': section_title,
                        'content': None,
                        'error': str(e),
                        'generation_method': 'multi_agent_exception'
                    }

            # Calculate overall statistics
            success_rate = (generation_summary['successful_sections'] /
                          generation_summary['total_sections'] * 100
                          if generation_summary['total_sections'] > 0 else 0)

            average_quality = (sum(generation_summary['quality_scores']) /
                             len(generation_summary['quality_scores'])
                             if generation_summary['quality_scores'] else None)

            logger.info(f"MULTI-AGENT: Generation complete - {generation_summary['successful_sections']}/{generation_summary['total_sections']} sections successful ({success_rate:.1f}%)")
            if average_quality:
                logger.info(f"MULTI-AGENT: Average quality score: {average_quality:.1f}/10")

            drafts = []
            for section in table_of_contents:
                section_title = section.get('title', 'Untitled Section')
                section_data = draft_sections.get(section_title, {})

                if section_data.get('content'):
                    # Create draft object in the same format as original generate_draft
                    draft_obj = {
                        'title': section_title,
                        'content': section_data['content'],
                        'number': section.get('number', ''),
                        'description': section.get('description', ''),
                        'is_cover_letter': self._is_cover_letter_section(section_title),
                        'subsections': []  # Multi-agent doesn't handle subsections yet
                    }
                    drafts.append(draft_obj)
                else:
                    # Even failed sections should be included with empty content
                    draft_obj = {
                        'title': section_title,
                        'content': '',
                        'number': section.get('number', ''),
                        'description': section.get('description', ''),
                        'is_cover_letter': self._is_cover_letter_section(section_title),
                        'subsections': []
                    }
                    drafts.append(draft_obj)

            # Return in the exact same format as generate_draft
            return {
                "draft": drafts
            }

        except ImportError as e:
            logger.error(f"MULTI-AGENT: Multi-agent system not available: {e}")
            logger.info(f"MULTI-AGENT: Falling back to traditional generation method")
            # Fallback to traditional method if multi-agent system is not available
            return await self.generate_draft(
                opportunity_id, tenant_id, source, client_short_name,
                tenant_metadata, table_of_contents
            )

        except Exception as e:
            logger.error(f"MULTI-AGENT: Multi-agent generation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure quality
            raise Exception(f"Multi-agent draft generation failed: {e}")

    def _map_section_to_agent_type(self, section_title: str) -> str:
        """
        Map section title to appropriate agent type for the multi-agent system.

        Enhanced to handle different types of letters and prevent LLM from
        repeating cover letter content for other letter types.

        Args:
            section_title: Title of the section

        Returns:
            Agent type string for the multi-agent system
        """
        title_lower = section_title.lower()

        # Enhanced letter detection - handle all types of letters specifically
        if any(term in title_lower for term in [
            'cover letter', 'cover', 'transmittal letter', 'transmittal',
            'introduction letter', 'introduction', 'proposal letter'
        ]):
            return 'cover_letter'

        # Specific handling for other letter types to prevent cover letter repetition
        elif any(term in title_lower for term in [
            'tentative offer letter', 'contingent offer letter', 'offer letter',
            'tentative', 'contingent', 'award letter', 'notification letter'
        ]):
            # Use custom type with specific instructions for these letter types
            return 'custom'

        # Executive content
        elif any(term in title_lower for term in ['executive', 'summary', 'overview']):
            return 'executive_summary'

        # Technical content
        elif any(term in title_lower for term in ['technical', 'approach', 'solution', 'methodology', 'architecture']):
            return 'technical_approach'

        # Management content
        elif any(term in title_lower for term in ['management', 'plan', 'project', 'organization', 'team']):
            return 'management_plan'
        elif any(term in title_lower for term in ['past', 'performance', 'experience', 'case', 'history']):
            return 'past_performance'

        # Compliance and requirements
        elif any(term in title_lower for term in ['compliance', 'requirement', 'regulation', 'standard']):
            return 'compliance'

        # Pricing and financial
        elif any(term in title_lower for term in ['pricing', 'cost', 'budget', 'financial', 'price']):
            return 'pricing'

        # Appendix and supporting materials
        elif any(term in title_lower for term in ['appendix', 'attachment', 'supporting', 'exhibit']):
            return 'appendix'

        # Default to custom for unknown section types
        else:
            return 'custom'

    def _is_cover_letter_section(self, section_title: str) -> bool:
        """
        Determine if a section is a cover letter type section.

        This addresses the issue where LLM kept repeating cover letter content
        for different types of letters (Tentative/Contingent Offer Letter, etc.)

        Args:
            section_title: Title of the section

        Returns:
            True if this is any type of letter section
        """
        title_lower = section_title.lower()

        # Expanded detection for all types of letters
        letter_keywords = [
            'cover', 'letter', 'transmittal', 'introduction',
            'tentative', 'contingent', 'offer', 'proposal letter',
            'cover letter', 'introduction letter', 'transmittal letter'
        ]

        return any(keyword in title_lower for keyword in letter_keywords)
    