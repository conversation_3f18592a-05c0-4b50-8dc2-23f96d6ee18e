import asyncio
from typing import Any, Dict, List, Optional

from loguru import logger
from services.chroma.chroma_service import ChromaService
from utils.llm import KontratarLLM
from database import get_kontratar_db
from pydantic import BaseModel, Field
from langchain_ollama import ChatOllama
from enum import Enum

class VolumeTitle(str, Enum):
    VOLUME_I = "Volume I"
    VOLUME_II = "Volume II"
    VOLUME_III = "Volume III"
    VOLUME_IV = "Volume IV"
    VOLUME_V = "Volume V"
    RFI = "RFI"  # If you want to allow "RFI" as a valid value


class ContentCompliance(BaseModel):
    content: str = Field(description="The expected content to be seen in the response for this volume")
    volume_title: VolumeTitle = Field(description="The volume that is expected to be seen in the response eg. Volume I, Volume II, all the way to Volume V. It cannot be beyond these these volumes")

class ContentComplianceResponse(BaseModel):
    content_compliance: List[ContentCompliance]


class ContentComplianceService:
    """
    Service for generating content compliance context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.max_tokens = 3096
        self.chroma_service = ChromaService(embedding_api_url, None)
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx = 6000,
            num_predict=self.max_tokens,
            temperature=0,
            base_url=llm_api_url,
            timeout=300  # 5 minute timeout
        )

    async def generate_content_compliance(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        is_rfp: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate content compliance output using ChromaDB and LLM.
        Returns a dict with 'content' (LLM output) and 'context' (list of cleaned chunks).
        """
        logger.info(f"Starting generate_content_compliance for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}, is_rfp={is_rfp}")
        chroma_query = '''
            List the statements, sections, factors or tasks that the contractor should propose
            or include in their response as well as the different volumes to be submitted and the neccessary content for each volume.
            Get All Tasks/Factors needed to be shown in this response.
        '''
        
        # Initialize variables
        requirements_context = []
        context_str = ""

        async for db in get_kontratar_db():
            max_chunks = 6
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context_str = "\n\n".join(requirements_context)
            logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for content compliance.")
            break

        # logger.info(f"Context for LLM:\n{context_str}")

        message = "This is an RFP, Show me what is expected in each volume" if is_rfp else "This is an RFI"
        system_prompt = '''
            **Role:**
            You are a government contracting specialist with over 15+ years of experience drafting winning RFP and RFI responses for
            federal agencies. 
            
            **Task:**
            You task is to identify and return the NECCESSARY and REQUIRED content that should be seen in an RFP or RFI response.
            You are not creating the response itself, RATHER you are identifying the content that should be in the response.
            ALL the complete information and attachments for the RFI or RFP have been included within <context>.
            USING that information to identify the NESSESARY and REQUIRED content that must be seen in the RFI or RFP respnse.
            If it is an RFP, separate the content to be seen for each Volume.
            You will be told if this an RFI or an RFP.

            **Rules:**
            1. YOU MUST identify the required content based off the information found in <context>
            2. IF THERE is EXPLICIT mention of TASKS or FACTORS that should be responded to, PLEASE inlcude them and BE EXTEREMELY DETAILED as possible with the
            FACTORS or TASKS as they are EXTREMELY important.
            3. IF THERE is EXPLICIT mention of PERSONNEL and how they should be included in the RFP response, please INCLUDE that in your response.
            4. IF THERE are any EXPLICIT mentions of any PLANS or APPROACHES (eg. Technical, Management, Security, etc) INCLUDE them in your response.
            5. ENSURE to mention any other NECCESARY content that MUST BE included in the RFI or RFP response.
            6. This is NOT a response to be submitted, SO DO NOT write it like such.
            7. DO NOT summarize!
            8. No placeholders FOR ANY REASON
            9. DO NOT INCLUDE meta-phrases like "Based on the provided context, here is the necessary and required content"
        '''

        user_prompt = f'''
        <context>
            {context_str}
        </context>

        {message}

        USE ONLY information found on context to build the response.

        '''
        messages = [
            ("system", system_prompt),
            ("human", user_prompt)
        ]

        logger.info("Invoking LLM for content compliance...")

        # Retry logic with shorter timeouts
        max_attempts = 3
        timeout_seconds = 180  # 3 minutes
        content = None

        for attempt in range(max_attempts):
            try:
                logger.info(f"LLM attempt {attempt + 1}/{max_attempts} for content compliance")
                content = await asyncio.wait_for(
                    asyncio.to_thread(self.llm.invoke, messages),
                    timeout=timeout_seconds
                )
                logger.info("LLM invocation successful for content compliance.")
                break
            except asyncio.TimeoutError:
                logger.error(f"LLM invocation timed out for content compliance (attempt {attempt + 1}) after {timeout_seconds} seconds")
                if attempt == max_attempts - 1:
                    raise Exception(f"LLM request timed out after {max_attempts} attempts")
                await asyncio.sleep(2)  # Wait before retry
            except Exception as e:
                logger.error(f"LLM invocation failed for content compliance (attempt {attempt + 1}): {e}")
                if attempt == max_attempts - 1:
                    raise
                await asyncio.sleep(2)  # Wait before retry

        if content is None:
            raise Exception("Failed to get LLM response after all attempts")

        logger.debug(f"LLM content compliance output: {content.content[:500]}{'...' if len(content.content) > 500 else ''}")
        return {"content": content.content, "context": requirements_context} 

    async def generate_sow_tasks(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
    ) -> str:
        """
        Generate specific program tasks outlined in RFP using ChromaDB and LLM.
        Returns a string which is the final output
        """
        logger.info(f"Starting generate_sow_tasks for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        chroma_query = '''
            List the tasks/program tasks that the contractor must respond to
        '''
        
        # Initialize variables
        context_str = ""

        async for db in get_kontratar_db():
            max_chunks = 5
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context_str = "\n\n".join(requirements_context)
            logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for SOW tasks.")
            break

        system_prompt = '''
            **Role:**
            You are a government contracting specialist with over 15+ years of experience drafting winning RFP and RFI responses for
            federal agencies. 
            
            **Task:**
            You task is to identify and return the SPECIFIC tasks/programs outlined in the RFP statement of work.
            You MUST explain EACH TASK in depth and much detail. Explain what is expected for each task to be performed successfully, subtasks if any.

            **Rules:**
            1. USE the information found in <context> to generate your answer
            2. DO NOT summarize!
            3. No placeholders FOR ANY REASON
            4. DO NOT INCLUDE meta-phrases like "Based on the provided context, here is the necessary and required content"
        '''

        user_prompt = f'''
        Return the different tasks stated in the SOW expected to be performed AS WELL as the specific activities to be done.

        <context>
            {context_str}
        </context>

        USE ONLY information found on context to build the response.
        DO NOT return anything before or after the tasks.

        '''
        messages = [
            ("system", system_prompt),
            ("user", user_prompt)
        ]
        logger.info("Invoking LLM for SOW tasks.")

        # Retry logic with shorter timeouts
        max_attempts = 3
        timeout_seconds = 180  # 3 minutes
        result = None

        for attempt in range(max_attempts):
            try:
                logger.info(f"LLM attempt {attempt + 1}/{max_attempts} for SOW tasks")
                result = await asyncio.wait_for(
                    asyncio.to_thread(self.llm.invoke, messages),
                    timeout=timeout_seconds
                )
                logger.info("LLM invocation successful for SOW tasks.")
                break
            except asyncio.TimeoutError:
                logger.error(f"LLM invocation timed out for SOW tasks (attempt {attempt + 1}) after {timeout_seconds} seconds")
                if attempt == max_attempts - 1:
                    raise Exception(f"LLM request timed out after {max_attempts} attempts")
                await asyncio.sleep(2)  # Wait before retry
            except Exception as e:
                logger.error(f"LLM invocation failed for SOW tasks (attempt {attempt + 1}): {e}")
                if attempt == max_attempts - 1:
                    raise
                await asyncio.sleep(2)  # Wait before retry

        if result is None:
            raise Exception("Failed to get LLM response after all attempts")

        logger.debug(f"LLM SOW tasks output: {str(result.content)[:500]}{'...' if len(str(result.content)) > 500 else ''}")
        return str(result.content)

    async def generate(self, opportunity_id: str, tenant_id: str, source: str) -> str:
        sow_tasks = await self.generate_sow_tasks(opportunity_id, tenant_id, source)
        volumes_content = await self.generate_content_compliance(opportunity_id, tenant_id, source)

        return f"""
        {volumes_content["content"]}
        {sow_tasks}
        """

